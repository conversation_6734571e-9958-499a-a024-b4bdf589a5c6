import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { finalisePayslips, reopenPayslips } from "@/services/employerDbService";

/**
 * Hook to finalise selected payslips and open next period
 */
export function useFinalisePayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (slipIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");

      // Pre-operation safety checks
      if (!Array.isArray(slipIds) || slipIds.length === 0) {
        throw new Error("No payslips selected for finalisation");
      }

      // Cancel all pending queries to prevent conflicts
      await queryClient.cancelQueries();

      // Trigger garbage collection before heavy operation
      if (process.env.NODE_ENV === "development" && (window as any).gc) {
        try {
          (window as any).gc();
        } catch {}
      }

      return finalisePayslips(dbPath, periodId, slipIds);
    },
    // Disable optimistic updates for this critical operation to prevent crashes
    onMutate: async (slipIds: string[]) => {
      if (!dbPath) return;

      // Only cancel queries, no optimistic updates
      await queryClient.cancelQueries();

      console.log("[FinalisePayslips] Starting finalisation operation...");
      return {};
    },
    onError: (err, slipIds, context) => {
      console.error("[FinalisePayslips] Operation failed:", err);
      // Force refresh all data on error
      if (dbPath) {
        queryClient.invalidateQueries();
      }
    },
    onSuccess: async () => {
      console.log("[FinalisePayslips] Operation completed successfully");

      if (dbPath) {
        // Add a small delay to allow database to settle
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Invalidate all queries to ensure fresh data
        queryClient.invalidateQueries();

        console.log("[FinalisePayslips] Cache invalidated");
      }
    },
    // Always refetch after error or success
    onSettled: async () => {
      if (dbPath) {
        // Additional delay before final invalidation
        await new Promise((resolve) => setTimeout(resolve, 200));

        // Ensure all data is refreshed
        queryClient.invalidateQueries();
      }
    },
  });
}

/**
 * Hook to reopen selected payslips
 */
export function useReopenPayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (employeeIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      if (!periodId) throw new Error("Period ID not available");
      if (!Array.isArray(employeeIds) || employeeIds.length === 0) {
        throw new Error("No employees selected");
      }

      try {
        return await reopenPayslips(dbPath, periodId, employeeIds);
      } catch (error) {
        console.error("[useReopenPayslipsMutation] Error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      if (dbPath) {
        // Sequential cache invalidation to prevent race conditions
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslips", dbPath, periodId],
          });
        }, 0);

        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        }, 50);

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslipStatuses", dbPath, periodId],
          });
        }, 100);
      }
    },
    onError: (error) => {
      console.error("[useReopenPayslipsMutation] Mutation failed:", error);
    },
  });
}
