import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { finalisePayslips, reopenPayslips } from "@/services/employerDbService";

/**
 * Hook to finalise selected payslips and open next period
 */
export function useFinalisePayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (slipIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      return finalisePayslips(dbPath, periodId, slipIds);
    },
    // Optimistic update for better UX
    onMutate: async (slipIds: string[]) => {
      if (!dbPath) return;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["payslipStatuses", dbPath, periodId],
      });

      // Snapshot the previous value
      const previousStatuses = queryClient.getQueryData([
        "payslipStatuses",
        dbPath,
        periodId,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(
        ["payslipStatuses", dbPath, periodId],
        (old: any) => {
          if (!old) return old;
          const updated = { ...old };
          slipIds.forEach((slipId) => {
            // Find employee ID for this slip ID (this is a simplification)
            // In practice, you'd need to map slip IDs to employee IDs
            updated[slipId] = "closed";
          });
          return updated;
        },
      );

      // Return a context object with the snapshotted value
      return { previousStatuses };
    },
    onError: (err, slipIds, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousStatuses && dbPath) {
        queryClient.setQueryData(
          ["payslipStatuses", dbPath, periodId],
          context.previousStatuses,
        );
      }
    },
    onSuccess: () => {
      if (dbPath) {
        // Strategic invalidation - only invalidate what's necessary
        queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        queryClient.invalidateQueries({
          queryKey: ["payslipStatuses", dbPath, periodId],
        });
        // Invalidate related payslip data
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath],
        });
      }
    },
    // Always refetch after error or success
    onSettled: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslipStatuses", dbPath, periodId],
        });
      }
    },
  });
}

/**
 * Hook to reopen selected payslips
 */
export function useReopenPayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (employeeIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      if (!periodId) throw new Error("Period ID not available");
      if (!Array.isArray(employeeIds) || employeeIds.length === 0) {
        throw new Error("No employees selected");
      }

      try {
        return await reopenPayslips(dbPath, periodId, employeeIds);
      } catch (error) {
        console.error("[useReopenPayslipsMutation] Error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      if (dbPath) {
        // Sequential cache invalidation to prevent race conditions
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslips", dbPath, periodId],
          });
        }, 0);

        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        }, 50);

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslipStatuses", dbPath, periodId],
          });
        }, 100);
      }
    },
    onError: (error) => {
      console.error("[useReopenPayslipsMutation] Mutation failed:", error);
    },
  });
}
