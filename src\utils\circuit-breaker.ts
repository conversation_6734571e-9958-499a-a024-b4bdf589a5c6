/**
 * Circuit breaker pattern to prevent cascading failures
 * during rapid navigation or database issues
 */

enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Failing, reject requests
  HALF_OPEN = 'HALF_OPEN' // Testing if service recovered
}

interface CircuitBreakerOptions {
  failureThreshold: number;  // Number of failures before opening
  resetTimeout: number;      // Time to wait before trying again (ms)
  monitoringPeriod: number;  // Time window for failure counting (ms)
}

class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private nextAttemptTime: number = 0;
  private failures: number[] = []; // Timestamps of failures

  constructor(private options: CircuitBreakerOptions) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttemptTime) {
        throw new Error('Circuit breaker is OPEN - operation rejected');
      }
      // Try to transition to half-open
      this.state = CircuitState.HALF_OPEN;
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failureCount = 0;
    this.failures = [];
    this.state = CircuitState.CLOSED;
  }

  private onFailure() {
    const now = Date.now();
    this.lastFailureTime = now;
    this.failures.push(now);

    // Clean up old failures outside monitoring period
    this.failures = this.failures.filter(
      time => now - time < this.options.monitoringPeriod
    );

    this.failureCount = this.failures.length;

    if (this.failureCount >= this.options.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = now + this.options.resetTimeout;
    }
  }

  getState(): CircuitState {
    return this.state;
  }

  getStats() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime,
    };
  }

  // Force reset the circuit breaker
  reset() {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.failures = [];
    this.lastFailureTime = 0;
    this.nextAttemptTime = 0;
  }
}

// Global circuit breakers for different operation types
const circuitBreakers = new Map<string, CircuitBreaker>();

// Default options for different operation types
const defaultOptions: Record<string, CircuitBreakerOptions> = {
  database: {
    failureThreshold: 5,
    resetTimeout: 5000,      // 5 seconds
    monitoringPeriod: 30000, // 30 seconds
  },
  payslip: {
    failureThreshold: 3,
    resetTimeout: 2000,      // 2 seconds
    monitoringPeriod: 10000, // 10 seconds
  },
  navigation: {
    failureThreshold: 10,
    resetTimeout: 1000,      // 1 second
    monitoringPeriod: 5000,  // 5 seconds
  },
};

export function getCircuitBreaker(
  name: string,
  type: keyof typeof defaultOptions = 'database'
): CircuitBreaker {
  if (!circuitBreakers.has(name)) {
    circuitBreakers.set(name, new CircuitBreaker(defaultOptions[type]));
  }
  return circuitBreakers.get(name)!;
}

// Helper function to execute operations with circuit breaker protection
export async function executeWithCircuitBreaker<T>(
  operationName: string,
  operation: () => Promise<T>,
  type: keyof typeof defaultOptions = 'database'
): Promise<T> {
  const circuitBreaker = getCircuitBreaker(operationName, type);
  return circuitBreaker.execute(operation);
}

// Get status of all circuit breakers (for debugging)
export function getCircuitBreakerStatus() {
  const status: Record<string, any> = {};
  for (const [name, breaker] of circuitBreakers.entries()) {
    status[name] = breaker.getStats();
  }
  return status;
}

// Reset all circuit breakers (for recovery)
export function resetAllCircuitBreakers() {
  for (const breaker of circuitBreakers.values()) {
    breaker.reset();
  }
}
