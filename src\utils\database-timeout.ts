/**
 * Database operation timeout utilities to prevent hanging operations
 * that could cause memory corruption or crashes
 */

export class DatabaseTimeoutError extends Error {
  constructor(operation: string, timeout: number) {
    super(`Database operation '${operation}' timed out after ${timeout}ms`);
    this.name = "DatabaseTimeoutError";
  }
}

/**
 * Wraps a database operation with a timeout to prevent hanging
 */
export async function withDatabaseTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number = 30000, // 30 seconds default
  operationName: string = "database operation",
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new DatabaseTimeoutError(operationName, timeoutMs));
    }, timeoutMs);

    operation()
      .then((result) => {
        clearTimeout(timeoutId);
        resolve(result);
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
  });
}

/**
 * Wraps a database transaction with timeout and retry logic
 */
export async function withDatabaseTransaction<T>(
  db: any,
  transactionFn: (tx: any) => Promise<T>,
  options: {
    timeoutMs?: number;
    retries?: number;
    operationName?: string;
  } = {},
): Promise<T> {
  const {
    timeoutMs = 30000,
    retries = 1,
    operationName = "database transaction",
  } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      return await withDatabaseTimeout(
        () => db.transaction(transactionFn),
        timeoutMs,
        `${operationName} (attempt ${attempt + 1})`,
      );
    } catch (error) {
      lastError = error as Error;

      // Don't retry timeout errors or certain critical errors
      if (
        error instanceof DatabaseTimeoutError ||
        (error instanceof Error &&
          (error.message.includes("database is locked") ||
            error.message.includes("database disk image is malformed")))
      ) {
        throw error;
      }

      // Log retry attempt
      if (attempt < retries) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        console.warn(
          `[DatabaseTimeout] ${operationName} failed, retrying (${attempt + 1}/${retries}):`,
          errorMessage,
        );
        // Wait a bit before retrying
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * (attempt + 1)),
        );
      }
    }
  }

  throw (
    lastError ||
    new Error(`${operationName} failed after ${retries + 1} attempts`)
  );
}

/**
 * Health check for database connections
 */
export async function checkDatabaseHealth(
  db: any,
  tableName: string = "payslips",
  timeoutMs: number = 5000,
): Promise<boolean> {
  try {
    await withDatabaseTimeout(
      async () => {
        // Simple query to test connection
        await db.select().from(tableName).limit(1).execute();
      },
      timeoutMs,
      "database health check",
    );
    return true;
  } catch (error) {
    console.error("[DatabaseTimeout] Health check failed:", error);
    return false;
  }
}

/**
 * Graceful database operation with fallback
 */
export async function withDatabaseFallback<T>(
  primaryOperation: () => Promise<T>,
  fallbackValue: T,
  operationName: string = "database operation",
  timeoutMs: number = 10000,
): Promise<T> {
  try {
    return await withDatabaseTimeout(
      primaryOperation,
      timeoutMs,
      operationName,
    );
  } catch (error) {
    console.warn(
      `[DatabaseTimeout] ${operationName} failed, using fallback:`,
      error,
    );
    return fallbackValue;
  }
}
