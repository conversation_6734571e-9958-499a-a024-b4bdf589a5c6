/**
 * Navigation throttling utilities to prevent crashes during rapid navigation
 */

interface NavigationState {
  lastNavigation: number;
  pendingNavigation: string | null;
  isNavigating: boolean;
}

class NavigationThrottle {
  private state: NavigationState = {
    lastNavigation: 0,
    pendingNavigation: null,
    isNavigating: false,
  };

  private readonly minInterval = 300; // Increased to 300ms between navigations
  private timeoutId: NodeJS.Timeout | null = null;

  /**
   * Throttle navigation to prevent rapid switching that causes crashes
   */
  throttleNavigation(
    navigationKey: string,
    navigationFn: () => void,
    forceImmediate: boolean = false,
  ): boolean {
    const now = Date.now();
    const timeSinceLastNav = now - this.state.lastNavigation;

    // Clear any pending navigation
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // If we're already navigating to the same target, ignore
    if (this.state.pendingNavigation === navigationKey) {
      return false;
    }

    // If enough time has passed or force immediate, navigate now
    if (timeSinceLastNav >= this.minInterval || forceImmediate) {
      this.executeNavigation(navigationKey, navigationFn);
      return true;
    }

    // Otherwise, schedule the navigation
    this.state.pendingNavigation = navigationKey;
    const delay = this.minInterval - timeSinceLastNav;

    this.timeoutId = setTimeout(() => {
      if (this.state.pendingNavigation === navigationKey) {
        this.executeNavigation(navigationKey, navigationFn);
      }
      this.timeoutId = null;
    }, delay);

    return false; // Navigation was throttled
  }

  private executeNavigation(navigationKey: string, navigationFn: () => void) {
    try {
      this.state.isNavigating = true;
      this.state.lastNavigation = Date.now();
      this.state.pendingNavigation = null;

      navigationFn();
    } catch (error) {
      console.error("[NavigationThrottle] Navigation error:", error);
    } finally {
      this.state.isNavigating = false;
    }
  }

  /**
   * Cancel any pending navigation
   */
  cancelPendingNavigation() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.state.pendingNavigation = null;
  }

  /**
   * Check if navigation is currently in progress
   */
  isNavigating(): boolean {
    return this.state.isNavigating;
  }

  /**
   * Get current navigation state for debugging
   */
  getState(): NavigationState {
    return { ...this.state };
  }

  /**
   * Reset the throttle state
   */
  reset() {
    this.cancelPendingNavigation();
    this.state = {
      lastNavigation: 0,
      pendingNavigation: null,
      isNavigating: false,
    };
  }
}

// Global navigation throttle instance
export const navigationThrottle = new NavigationThrottle();

/**
 * Hook for throttled navigation
 */
export function useNavigationThrottle() {
  const throttleNavigation = (
    navigationKey: string,
    navigationFn: () => void,
    forceImmediate: boolean = false,
  ) => {
    return navigationThrottle.throttleNavigation(
      navigationKey,
      navigationFn,
      forceImmediate,
    );
  };

  const cancelPendingNavigation = () => {
    navigationThrottle.cancelPendingNavigation();
  };

  const isNavigating = () => {
    return navigationThrottle.isNavigating();
  };

  return {
    throttleNavigation,
    cancelPendingNavigation,
    isNavigating,
  };
}

/**
 * Higher-order function to wrap navigation functions with throttling
 */
export function withNavigationThrottle<T extends any[]>(
  navigationFn: (...args: T) => void,
  getNavigationKey: (...args: T) => string,
) {
  return (...args: T) => {
    const navigationKey = getNavigationKey(...args);
    navigationThrottle.throttleNavigation(navigationKey, () =>
      navigationFn(...args),
    );
  };
}

/**
 * Utility to create a throttled employee/period navigation function
 */
export function createThrottledPayslipNavigation(
  navigationFn: (employeeId: string, periodId: string) => void,
) {
  return withNavigationThrottle(
    navigationFn,
    (employeeId: string, periodId: string) =>
      `payslip-${employeeId}-${periodId}`,
  );
}
