import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  getPayslip,
  getItemTypes,
  upsertPayslipLineItem,
  deletePayslipLineItem,
  addP<PERSON>lipNote,
  deletePayslipNote,
  createPayslip,
} from "@/services/employerDbService";
import type {
  Payslip,
  PayslipLineItem,
  PayslipItemType,
  PayslipNote,
  PayslipAdditionsLineItem,
} from "@/drizzle/schema/employer/payslip";

// Fetch payslip with items and notes
export function usePayslip(employeeId: string, periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;

  return useQuery<{
    payslip: Payslip | null;
    items: PayslipLineItem[];
    notes: PayslipNote[];
    additions: PayslipAdditionsLineItem[];
  }>({
    queryKey: ["payslip", dbPath, employeeId, periodId],
    queryFn: async ({ signal }) => {
      // Check if request was cancelled
      if (signal?.aborted) {
        throw new Error("Request cancelled");
      }

      try {
        return await getPayslip(dbPath!, employeeId, periodId);
      } catch (error) {
        // Don't throw if the request was cancelled
        if (signal?.aborted) {
          throw new Error("Request cancelled");
        }
        throw error;
      }
    },
    enabled: !!dbPath && !!employeeId && !!periodId,
    // Payslip data changes frequently during editing - shorter stale time
    staleTime: 30 * 1000, // 30 seconds
    // Keep in cache for 5 minutes for quick navigation
    gcTime: 5 * 60 * 1000,
  });
}

// Fetch item types
export function usePayslipItemTypes() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  return useQuery<PayslipItemType[]>({
    queryKey: ["payslipItemTypes", dbPath],
    queryFn: () => getItemTypes(dbPath!),
    enabled: !!dbPath,
    // Item types rarely change - longer stale time
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}

// Upsert line item
export function useUpsertPayslipLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (item: Partial<PayslipLineItem> & { payslip_id: string }) =>
      upsertPayslipLineItem(dbPath!, item),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
    },
  });
}

// Delete line item
export function useDeletePayslipLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => deletePayslipLineItem(dbPath!, id),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
    },
  });
}

// Add payslip note
export function useAddPayslipNoteMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      payslipId,
      content,
    }: {
      payslipId: string;
      content: string;
    }) => addPayslipNote(dbPath!, payslipId, content),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
    },
  });
}

// Delete payslip note
export function useDeletePayslipNoteMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (noteId: string) => deletePayslipNote(dbPath!, noteId),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
    },
  });
}

// Create payslip
export function useCreatePayslipMutation(employeeId: string, periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => createPayslip(dbPath!, employeeId, periodId),
    onSuccess: () => {
      if (dbPath)
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
    },
  });
}
