/**
 * Operation mutex to prevent concurrent critical operations
 * that could cause crashes or data corruption
 */

interface MutexEntry {
  operationId: string;
  timestamp: number;
  resolve: () => void;
  reject: (error: Error) => void;
}

class OperationMutex {
  private activeMutexes = new Map<string, boolean>();
  private pendingOperations = new Map<string, MutexEntry[]>();
  private readonly maxWaitTime = 30000; // 30 seconds max wait

  /**
   * Acquire a mutex for a critical operation
   */
  async acquire(mutexKey: string, operationId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // If mutex is not active, acquire immediately
      if (!this.activeMutexes.get(mutexKey)) {
        this.activeMutexes.set(mutexKey, true);
        resolve();
        return;
      }

      // Otherwise, queue the operation
      if (!this.pendingOperations.has(mutexKey)) {
        this.pendingOperations.set(mutexKey, []);
      }

      const entry: MutexEntry = {
        operationId,
        timestamp: Date.now(),
        resolve,
        reject,
      };

      this.pendingOperations.get(mutexKey)!.push(entry);

      // Set timeout for the operation
      setTimeout(() => {
        this.rejectOperation(
          mutexKey,
          operationId,
          new Error("Mutex acquisition timeout"),
        );
      }, this.maxWaitTime);
    });
  }

  /**
   * Release a mutex and process next pending operation
   */
  release(mutexKey: string): void {
    this.activeMutexes.set(mutexKey, false);

    // Process next pending operation
    const pending = this.pendingOperations.get(mutexKey);
    if (pending && pending.length > 0) {
      const next = pending.shift()!;
      this.activeMutexes.set(mutexKey, true);
      next.resolve();

      // Clean up empty queue
      if (pending.length === 0) {
        this.pendingOperations.delete(mutexKey);
      }
    }
  }

  /**
   * Check if a mutex is currently active
   */
  isActive(mutexKey: string): boolean {
    return this.activeMutexes.get(mutexKey) || false;
  }

  /**
   * Get the number of pending operations for a mutex
   */
  getPendingCount(mutexKey: string): number {
    return this.pendingOperations.get(mutexKey)?.length || 0;
  }

  /**
   * Reject a specific operation
   */
  private rejectOperation(
    mutexKey: string,
    operationId: string,
    error: Error,
  ): void {
    const pending = this.pendingOperations.get(mutexKey);
    if (!pending) return;

    const index = pending.findIndex(
      (entry) => entry.operationId === operationId,
    );
    if (index !== -1) {
      const entry = pending.splice(index, 1)[0];
      entry.reject(error);

      // Clean up empty queue
      if (pending.length === 0) {
        this.pendingOperations.delete(mutexKey);
      }
    }
  }

  /**
   * Cancel all pending operations for a mutex
   */
  cancelPending(mutexKey: string): void {
    const pending = this.pendingOperations.get(mutexKey);
    if (pending) {
      pending.forEach((entry) => {
        entry.reject(new Error("Operation cancelled"));
      });
      this.pendingOperations.delete(mutexKey);
    }
  }

  /**
   * Force release a mutex (use with caution)
   */
  forceRelease(mutexKey: string): void {
    this.activeMutexes.set(mutexKey, false);
    this.cancelPending(mutexKey);
  }

  /**
   * Get status of all mutexes for debugging
   */
  getStatus(): Record<string, { active: boolean; pending: number }> {
    const status: Record<string, { active: boolean; pending: number }> = {};

    for (const [key, active] of this.activeMutexes.entries()) {
      status[key] = {
        active,
        pending: this.getPendingCount(key),
      };
    }

    return status;
  }

  /**
   * Clear all mutexes (for cleanup)
   */
  clear(): void {
    // Cancel all pending operations
    for (const mutexKey of this.pendingOperations.keys()) {
      this.cancelPending(mutexKey);
    }

    this.activeMutexes.clear();
    this.pendingOperations.clear();
  }
}

// Global mutex instance
export const operationMutex = new OperationMutex();

/**
 * Higher-order function to wrap operations with mutex protection
 */
export function withMutex<T extends any[], R>(
  mutexKey: string,
  operation: (...args: T) => Promise<R>,
) {
  return async (...args: T): Promise<R> => {
    const operationId = `${mutexKey}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    try {
      await operationMutex.acquire(mutexKey, operationId);
      const result = await operation(...args);
      return result;
    } finally {
      operationMutex.release(mutexKey);
    }
  };
}

/**
 * React hook for mutex-protected operations
 */
export function useMutex(mutexKey: string) {
  const acquire = (operationId?: string) => {
    const id = operationId || `${mutexKey}-${Date.now()}`;
    return operationMutex.acquire(mutexKey, id);
  };

  const release = () => {
    operationMutex.release(mutexKey);
  };

  const isActive = () => {
    return operationMutex.isActive(mutexKey);
  };

  const getPendingCount = () => {
    return operationMutex.getPendingCount(mutexKey);
  };

  return {
    acquire,
    release,
    isActive,
    getPendingCount,
  };
}

/**
 * Predefined mutex keys for common operations
 */
export const MUTEX_KEYS = {
  FINALISE_PAYSLIPS: "finalise-payslips",
  REOPEN_PAYSLIPS: "reopen-payslips",
  PERIOD_SWITCH: "period-switch",
  DATABASE_MIGRATION: "database-migration",
} as const;
