/**
 * Aggressive crash prevention system for rapid navigation scenarios
 */

import { memoryMonitor } from './memory-monitor';
import { operationMutex } from './operation-mutex';

interface CrashPreventionState {
  rapidNavigationCount: number;
  lastNavigationTime: number;
  isInCriticalState: boolean;
  emergencyMode: boolean;
  blockedOperations: Set<string>;
}

class CrashPreventionSystem {
  private state: CrashPreventionState = {
    rapidNavigationCount: 0,
    lastNavigationTime: 0,
    isInCriticalState: false,
    emergencyMode: false,
    blockedOperations: new Set(),
  };

  private readonly RAPID_THRESHOLD = 5; // 5 navigations
  private readonly RAPID_WINDOW = 2000; // within 2 seconds
  private readonly EMERGENCY_COOLDOWN = 5000; // 5 second emergency cooldown
  private readonly MEMORY_THRESHOLD = 150 * 1024 * 1024; // 150MB

  /**
   * Check if an operation should be allowed
   */
  shouldAllowOperation(operationType: string, operationKey?: string): boolean {
    const now = Date.now();
    
    // Always block if in emergency mode
    if (this.state.emergencyMode) {
      console.warn(`[CrashPrevention] Operation blocked - emergency mode: ${operationType}`);
      return false;
    }

    // Check if this specific operation is blocked
    const fullKey = operationKey ? `${operationType}:${operationKey}` : operationType;
    if (this.state.blockedOperations.has(fullKey)) {
      console.warn(`[CrashPrevention] Operation blocked - specific block: ${fullKey}`);
      return false;
    }

    // Track navigation frequency
    if (operationType === 'navigation') {
      this.trackNavigation(now);
    }

    // Check memory pressure
    this.checkMemoryPressure();

    return !this.state.isInCriticalState;
  }

  /**
   * Track navigation frequency and detect rapid switching
   */
  private trackNavigation(now: number): void {
    const timeSinceLastNav = now - this.state.lastNavigationTime;
    
    if (timeSinceLastNav < this.RAPID_WINDOW) {
      this.state.rapidNavigationCount++;
    } else {
      // Reset counter if outside the rapid window
      this.state.rapidNavigationCount = 1;
    }

    this.state.lastNavigationTime = now;

    // Enter critical state if too many rapid navigations
    if (this.state.rapidNavigationCount >= this.RAPID_THRESHOLD) {
      this.enterCriticalState();
    }
  }

  /**
   * Enter critical state with aggressive protection
   */
  private enterCriticalState(): void {
    console.warn('[CrashPrevention] Entering critical state - rapid navigation detected');
    
    this.state.isInCriticalState = true;
    this.state.emergencyMode = true;

    // Cancel all pending operations
    operationMutex.clear();

    // Force garbage collection if available
    this.forceGarbageCollection();

    // Set emergency cooldown
    setTimeout(() => {
      this.exitEmergencyMode();
    }, this.EMERGENCY_COOLDOWN);
  }

  /**
   * Exit emergency mode but stay in critical state briefly
   */
  private exitEmergencyMode(): void {
    console.log('[CrashPrevention] Exiting emergency mode');
    this.state.emergencyMode = false;
    
    // Exit critical state after additional delay
    setTimeout(() => {
      this.exitCriticalState();
    }, 2000);
  }

  /**
   * Exit critical state completely
   */
  private exitCriticalState(): void {
    console.log('[CrashPrevention] Exiting critical state');
    this.state.isInCriticalState = false;
    this.state.rapidNavigationCount = 0;
    this.state.blockedOperations.clear();
  }

  /**
   * Check memory pressure and take action
   */
  private checkMemoryPressure(): void {
    const memoryStats = memoryMonitor.getMemoryStats();
    if (memoryStats && memoryStats.usedJSHeapSize > this.MEMORY_THRESHOLD) {
      console.warn('[CrashPrevention] High memory usage detected');
      this.forceGarbageCollection();
      
      // Enter critical state if memory is very high
      if (memoryStats.usedJSHeapSize > this.MEMORY_THRESHOLD * 1.5) {
        this.enterCriticalState();
      }
    }
  }

  /**
   * Force garbage collection
   */
  private forceGarbageCollection(): void {
    try {
      if (process.env.NODE_ENV === 'development' && (window as any).gc) {
        (window as any).gc();
        console.log('[CrashPrevention] Forced garbage collection');
      }
    } catch (error) {
      // Ignore errors
    }
  }

  /**
   * Block a specific operation temporarily
   */
  blockOperation(operationType: string, operationKey?: string, durationMs: number = 1000): void {
    const fullKey = operationKey ? `${operationType}:${operationKey}` : operationType;
    this.state.blockedOperations.add(fullKey);
    
    setTimeout(() => {
      this.state.blockedOperations.delete(fullKey);
    }, durationMs);
  }

  /**
   * Get current system state
   */
  getState(): CrashPreventionState {
    return { ...this.state };
  }

  /**
   * Reset the system (for testing or recovery)
   */
  reset(): void {
    this.state = {
      rapidNavigationCount: 0,
      lastNavigationTime: 0,
      isInCriticalState: false,
      emergencyMode: false,
      blockedOperations: new Set(),
    };
  }

  /**
   * Manual trigger for critical state (for testing)
   */
  triggerEmergencyMode(): void {
    this.enterCriticalState();
  }
}

// Global crash prevention instance
export const crashPrevention = new CrashPreventionSystem();

/**
 * Higher-order function to wrap operations with crash prevention
 */
export function withCrashPrevention<T extends any[], R>(
  operationType: string,
  operation: (...args: T) => R,
  getOperationKey?: (...args: T) => string
) {
  return (...args: T): R | null => {
    const operationKey = getOperationKey ? getOperationKey(...args) : undefined;
    
    if (!crashPrevention.shouldAllowOperation(operationType, operationKey)) {
      console.warn(`[CrashPrevention] Blocked ${operationType} operation`);
      return null;
    }

    try {
      return operation(...args);
    } catch (error) {
      console.error(`[CrashPrevention] Error in ${operationType}:`, error);
      // Block this operation temporarily after error
      crashPrevention.blockOperation(operationType, operationKey, 2000);
      throw error;
    }
  };
}

/**
 * React hook for crash prevention
 */
export function useCrashPrevention() {
  const shouldAllowOperation = (operationType: string, operationKey?: string) => {
    return crashPrevention.shouldAllowOperation(operationType, operationKey);
  };

  const blockOperation = (operationType: string, operationKey?: string, duration?: number) => {
    crashPrevention.blockOperation(operationType, operationKey, duration);
  };

  const getState = () => {
    return crashPrevention.getState();
  };

  const isInCriticalState = () => {
    return crashPrevention.getState().isInCriticalState;
  };

  const isInEmergencyMode = () => {
    return crashPrevention.getState().emergencyMode;
  };

  return {
    shouldAllowOperation,
    blockOperation,
    getState,
    isInCriticalState,
    isInEmergencyMode,
  };
}

/**
 * Specific crash prevention for employee navigation
 */
export function createCrashSafeEmployeeNavigation<T extends any[]>(
  navigationFn: (...args: T) => void
) {
  return withCrashPrevention(
    'employee-navigation',
    navigationFn,
    (...args) => `employee-${args[0]}` // Assume first arg is employee ID
  );
}

/**
 * Initialize crash prevention monitoring
 */
export function initializeCrashPrevention() {
  console.log('[CrashPrevention] System initialized');
  
  // Monitor for unhandled errors
  window.addEventListener('error', (event) => {
    console.error('[CrashPrevention] Unhandled error detected:', event.error);
    crashPrevention.triggerEmergencyMode();
  });

  // Monitor for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('[CrashPrevention] Unhandled rejection detected:', event.reason);
    crashPrevention.triggerEmergencyMode();
  });
}
