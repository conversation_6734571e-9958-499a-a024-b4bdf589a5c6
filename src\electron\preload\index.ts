import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import type { CreateEmployerDbParams } from "../file-handlers/createEmployerDb";

// Handle memory warnings from main process
ipcRenderer.on("memory-warning", (_event, data) => {
  console.warn("[Preload] Memory warning received:", data);
  // Use globalThis to access window in preload context
  if (typeof globalThis !== "undefined" && globalThis.window) {
    globalThis.window.dispatchEvent(
      new globalThis.CustomEvent("memory-warning", { detail: data }),
    );
  }
});

// Handle critical memory warnings
ipcRenderer.on("memory-critical", (_event, data) => {
  console.error("[Preload] CRITICAL memory warning received:", data);
  // Use globalThis to access window in preload context
  if (typeof globalThis !== "undefined" && globalThis.window) {
    globalThis.window.dispatchEvent(
      new globalThis.CustomEvent("memory-critical", { detail: data }),
    );
  }
});

contextBridge.exposeInMainWorld("api", {
  createEmployerDb: async (params: CreateEmployerDbParams) => {
    return await ipcRenderer.invoke("employer:create-db", params);
  },
  getEmployers: async () => {
    return await ipcRenderer.invoke("employers:list");
  },
  addExistingEmployer: async (filePath: string) => {
    return await ipcRenderer.invoke("employer:add-existing", filePath);
  },
  removeEmployer: async (employerId: string) => {
    return await ipcRenderer.invoke("employer:remove", employerId);
  },
  getEmployer: async (dbPath: string) => {
    return await ipcRenderer.invoke("employer:get", dbPath);
  },
  updateEmployer: async (dbPath: string, employer: any) => {
    return await ipcRenderer.invoke("employer:update", dbPath, employer);
  },
  invoke: (channel: string, ...args: any[]) =>
    ipcRenderer.invoke(channel, ...args),
  // Add more safe APIs here as needed
});
