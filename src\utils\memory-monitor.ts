/**
 * Memory monitoring utilities to detect and prevent memory leaks
 * that could lead to crashes during rapid navigation
 */

import React from "react";

interface MemoryStats {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: number;
}

interface ComponentTracker {
  mountCount: number;
  unmountCount: number;
  activeInstances: Set<string>;
}

class MemoryMonitor {
  private isMonitoring = false;
  private monitorInterval: NodeJS.Timeout | null = null;
  private memoryHistory: MemoryStats[] = [];
  private componentTrackers = new Map<string, ComponentTracker>();
  private readonly maxHistorySize = 50;
  private readonly memoryThreshold = 100 * 1024 * 1024; // 100MB threshold

  /**
   * Start monitoring memory usage
   */
  startMonitoring(intervalMs: number = 5000) {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitorInterval = setInterval(() => {
      this.collectMemoryStats();
      this.checkMemoryThreshold();
    }, intervalMs);

    console.log("[MemoryMonitor] Started monitoring");
  }

  /**
   * Stop monitoring memory usage
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    this.isMonitoring = false;
    console.log("[MemoryMonitor] Stopped monitoring");
  }

  /**
   * Collect current memory statistics
   */
  private collectMemoryStats() {
    if (typeof window === "undefined" || !window.performance?.memory) {
      return;
    }

    const memory = window.performance.memory;
    const stats: MemoryStats = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      timestamp: Date.now(),
    };

    this.memoryHistory.push(stats);

    // Keep only recent history
    if (this.memoryHistory.length > this.maxHistorySize) {
      this.memoryHistory.shift();
    }
  }

  /**
   * Check if memory usage exceeds threshold
   */
  private checkMemoryThreshold() {
    const latest = this.memoryHistory[this.memoryHistory.length - 1];
    if (!latest) return;

    if (latest.usedJSHeapSize > this.memoryThreshold) {
      console.warn("[MemoryMonitor] Memory usage high:", {
        used: `${(latest.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        total: `${(latest.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        limit: `${(latest.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
      });

      // Trigger garbage collection if available
      this.triggerGarbageCollection();
    }
  }

  /**
   * Trigger garbage collection if available
   */
  private triggerGarbageCollection() {
    try {
      // Force garbage collection in development
      if (process.env.NODE_ENV === "development" && (window as any).gc) {
        (window as any).gc();
        console.log("[MemoryMonitor] Triggered garbage collection");
      }
    } catch (error) {
      // Ignore errors
    }
  }

  /**
   * Track component mount/unmount
   */
  trackComponent(
    componentName: string,
    instanceId: string,
    action: "mount" | "unmount",
  ) {
    if (!this.componentTrackers.has(componentName)) {
      this.componentTrackers.set(componentName, {
        mountCount: 0,
        unmountCount: 0,
        activeInstances: new Set(),
      });
    }

    const tracker = this.componentTrackers.get(componentName)!;

    if (action === "mount") {
      tracker.mountCount++;
      tracker.activeInstances.add(instanceId);
    } else {
      tracker.unmountCount++;
      tracker.activeInstances.delete(instanceId);
    }

    // Warn about potential memory leaks
    const activeCount = tracker.activeInstances.size;
    if (activeCount > 10) {
      console.warn(
        `[MemoryMonitor] High active instance count for ${componentName}: ${activeCount}`,
      );
    }
  }

  /**
   * Get current memory statistics
   */
  getMemoryStats(): MemoryStats | null {
    return this.memoryHistory[this.memoryHistory.length - 1] || null;
  }

  /**
   * Get component tracking statistics
   */
  getComponentStats(): Map<string, ComponentTracker> {
    return new Map(this.componentTrackers);
  }

  /**
   * Get memory usage trend
   */
  getMemoryTrend(): "increasing" | "decreasing" | "stable" {
    if (this.memoryHistory.length < 5) return "stable";

    const recent = this.memoryHistory.slice(-5);
    const first = recent[0].usedJSHeapSize;
    const last = recent[recent.length - 1].usedJSHeapSize;
    const diff = last - first;
    const threshold = 5 * 1024 * 1024; // 5MB threshold

    if (diff > threshold) return "increasing";
    if (diff < -threshold) return "decreasing";
    return "stable";
  }

  /**
   * Clear all tracking data
   */
  reset() {
    this.memoryHistory = [];
    this.componentTrackers.clear();
  }
}

// Global memory monitor instance
export const memoryMonitor = new MemoryMonitor();

/**
 * React hook for component tracking
 */
export function useMemoryTracking(componentName: string) {
  const instanceId = React.useRef(Math.random().toString(36).substr(2, 9));

  React.useEffect(() => {
    memoryMonitor.trackComponent(componentName, instanceId.current, "mount");

    return () => {
      memoryMonitor.trackComponent(
        componentName,
        instanceId.current,
        "unmount",
      );
    };
  }, [componentName]);
}

/**
 * Initialize memory monitoring for the application
 */
export function initializeMemoryMonitoring() {
  if (process.env.NODE_ENV === "development") {
    memoryMonitor.startMonitoring(10000); // Monitor every 10 seconds in development
  }
}

/**
 * Get memory usage summary for debugging
 */
export function getMemorySummary() {
  const stats = memoryMonitor.getMemoryStats();
  const trend = memoryMonitor.getMemoryTrend();
  const componentStats = memoryMonitor.getComponentStats();

  return {
    memory: stats
      ? {
          used: `${(stats.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          total: `${(stats.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          limit: `${(stats.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
        }
      : null,
    trend,
    components: Array.from(componentStats.entries()).map(([name, tracker]) => ({
      name,
      active: tracker.activeInstances.size,
      mounted: tracker.mountCount,
      unmounted: tracker.unmountCount,
    })),
  };
}
