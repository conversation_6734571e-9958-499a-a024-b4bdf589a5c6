/**
 * Request queue manager to prevent too many concurrent database operations
 * that could cause memory corruption or crashes during rapid navigation
 */

interface QueuedRequest {
  id: string;
  fn: () => Promise<any>;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  timestamp: number;
}

class RequestQueue {
  private queue: QueuedRequest[] = [];
  private running: Set<string> = new Set();
  private maxConcurrent: number = 3; // Limit concurrent requests
  private requestTimeout: number = 10000; // 10 second timeout

  async add<T>(id: string, fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // Cancel any existing request with the same ID (for rapid navigation)
      this.cancel(id);

      const request: QueuedRequest = {
        id,
        fn,
        resolve,
        reject,
        timestamp: Date.now(),
      };

      this.queue.push(request);
      this.processQueue();
    });
  }

  private cancel(id: string) {
    // Remove from queue
    const index = this.queue.findIndex(req => req.id === id);
    if (index !== -1) {
      const request = this.queue.splice(index, 1)[0];
      request.reject(new Error('Request cancelled'));
    }

    // Mark running request as cancelled (it will be ignored when it completes)
    this.running.delete(id);
  }

  private async processQueue() {
    // Clean up old requests
    this.cleanupOldRequests();

    // Process queue if we have capacity
    while (this.queue.length > 0 && this.running.size < this.maxConcurrent) {
      const request = this.queue.shift()!;
      
      // Skip if this request was cancelled
      if (!this.queue.includes(request) && !this.running.has(request.id)) {
        this.running.add(request.id);
        this.executeRequest(request);
      }
    }
  }

  private async executeRequest(request: QueuedRequest) {
    const timeoutId = setTimeout(() => {
      this.running.delete(request.id);
      request.reject(new Error('Request timeout'));
    }, this.requestTimeout);

    try {
      const result = await request.fn();
      
      // Only resolve if the request wasn't cancelled
      if (this.running.has(request.id)) {
        clearTimeout(timeoutId);
        this.running.delete(request.id);
        request.resolve(result);
      }
    } catch (error) {
      // Only reject if the request wasn't cancelled
      if (this.running.has(request.id)) {
        clearTimeout(timeoutId);
        this.running.delete(request.id);
        request.reject(error);
      }
    }

    // Process next item in queue
    this.processQueue();
  }

  private cleanupOldRequests() {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds

    // Remove old requests from queue
    this.queue = this.queue.filter(request => {
      if (now - request.timestamp > maxAge) {
        request.reject(new Error('Request expired'));
        return false;
      }
      return true;
    });
  }

  // Get queue status for debugging
  getStatus() {
    return {
      queueLength: this.queue.length,
      runningCount: this.running.size,
      runningIds: Array.from(this.running),
    };
  }

  // Clear all pending requests (for app shutdown)
  clear() {
    this.queue.forEach(request => {
      request.reject(new Error('Queue cleared'));
    });
    this.queue = [];
    this.running.clear();
  }
}

// Global instance
export const requestQueue = new RequestQueue();

// Helper function to queue database operations
export function queueDatabaseOperation<T>(
  operationId: string,
  operation: () => Promise<T>
): Promise<T> {
  return requestQueue.add(operationId, operation);
}

// Helper to create unique operation IDs
export function createOperationId(type: string, ...params: string[]): string {
  return `${type}:${params.join(':')}:${Date.now()}`;
}
