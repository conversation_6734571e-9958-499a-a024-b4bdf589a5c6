import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payslip";
import * as pp from "../../drizzle/schema/employer/payPeriod";
import { inArray, eq, sql } from "drizzle-orm";
import { withDatabaseTransaction } from "../../utils/database-timeout";

// Fetch a payslip (may not exist yet)
ipcMain.handle(
  "employerDb:getPayslip",
  async (_event, dbPath: string, employeeId: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;

      // Use AND condition instead of chaining .where() calls
      const { and } = require("drizzle-orm");
      const [payslip] = await db
        .select()
        .from(schema.payslips)
        .where(
          and(
            eq(schema.payslips.employee_id, employeeId),
            eq(schema.payslips.period_id, periodId),
          ),
        )
        .all();

      if (!payslip) {
        return {
          success: true,
          payslip: null,
          items: [],
          notes: [],
          additions: [],
          deductions: [],
        };
      }
      const items = await db
        .select()
        .from(schema.payslipLineItems)
        .where(eq(schema.payslipLineItems.payslip_id, payslip.id))
        .all();
      const notes = await db
        .select()
        .from(schema.payslipNotes)
        .where(eq(schema.payslipNotes.payslip_id, payslip.id))
        .all();
      const additions = await db
        .select()
        .from(schema.payslipAdditionsLineItems)
        .where(eq(schema.payslipAdditionsLineItems.payslip_id, payslip.id))
        .all();
      const deductions = await db
        .select()
        .from(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslip.id))
        .all();

      return { success: true, payslip, items, notes, additions, deductions };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Fetch all item types
ipcMain.handle("employerDb:getItemTypes", async (_event, dbPath: string) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    let itemTypes = await db.select().from(schema.payslipItemTypes).all();
    // Seed default types if none exist
    if (itemTypes.length === 0) {
      const now = Math.floor(Date.now() / 1000);
      const defaults = [
        {
          id: "salary",
          code: "salary",
          section: "basic",
          display_label: "Salary",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
        {
          id: "daily",
          code: "daily",
          section: "basic",
          display_label: "Daily",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
        {
          id: "hourly",
          code: "hourly",
          section: "basic",
          display_label: "Hourly",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
      ];
      await db.insert(schema.payslipItemTypes).values(defaults).run();
      itemTypes = await db.select().from(schema.payslipItemTypes).all();
    }
    return { success: true, itemTypes };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Upsert a line item
ipcMain.handle(
  "employerDb:upsertPayslipLineItem",
  async (_event, dbPath: string, item: any) => {
    try {
      console.log("[IPC] Upserting line item:", {
        hasId: !!item.id,
        payslip_id: item.payslip_id,
        item_type_id: item.item_type_id,
      });

      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;

      // Validate required fields
      if (!item.payslip_id) {
        console.error("[IPC] Missing payslip_id in line item:", item);
        return { success: false, error: "Missing payslip_id" };
      }
      if (!item.item_type_id) {
        console.error("[IPC] Missing item_type_id in line item:", item);
        return { success: false, error: "Missing item_type_id" };
      }

      // Check if payslip exists
      const payslipExists = await db
        .select({ id: schema.payslips.id })
        .from(schema.payslips)
        .where(eq(schema.payslips.id, item.payslip_id))
        .get();

      if (!payslipExists) {
        console.error("[IPC] Payslip not found:", item.payslip_id);
        return {
          success: false,
          error: `Payslip ${item.payslip_id} not found`,
        };
      }

      const now = Math.floor(Date.now() / 1000);

      // Add small delay to prevent overwhelming the database
      await new Promise((resolve) => setTimeout(resolve, 10));

      if (!item.id) {
        const id = crypto.randomUUID();
        console.log("[IPC] Creating new line item with ID:", id);
        const [inserted] = await db
          .insert(schema.payslipLineItems)
          .values({ ...item, id, created_at: now, updated_at: now })
          .returning();
        console.log("[IPC] Line item created successfully");
        return { success: true, lineItem: inserted };
      } else {
        console.log("[IPC] Updating existing line item:", item.id);
        const [updated] = await db
          .update(schema.payslipLineItems)
          .set({ ...item, updated_at: now })
          .where(eq(schema.payslipLineItems.id, item.id))
          .returning();
        console.log("[IPC] Line item updated successfully");
        return { success: true, lineItem: updated };
      }
    } catch (err: any) {
      console.error("[IPC] Error in upsertPayslipLineItem:", err);
      return { success: false, error: err.message };
    }
  },
);

// Delete a line item
ipcMain.handle(
  "employerDb:deletePayslipLineItem",
  async (_event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipLineItems)
        .where(eq(schema.payslipLineItems.id, id));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add a payslip note
ipcMain.handle(
  "employerDb:addPayslipNote",
  async (_event, dbPath: string, payslipId: string, content: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const id = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [note] = await db
        .insert(schema.payslipNotes)
        .values({
          id,
          payslip_id: payslipId,
          content,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, note };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a payslip note
ipcMain.handle(
  "employerDb:deletePayslipNote",
  async (_event, dbPath: string, noteId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipNotes)
        .where(eq(schema.payslipNotes.id, noteId));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Create a new payslip record
ipcMain.handle(
  "employerDb:createPayslip",
  async (_event, dbPath: string, employeeId: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const id = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [inserted] = await db
        .insert(schema.payslips)
        .values({
          id,
          employee_id: employeeId,
          period_id: periodId,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, payslip: inserted };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Finalise selected payslips and open next period
ipcMain.handle(
  "employerDb:finalisePayslips",
  async (_event, dbPath: string, periodId: string, employeeIds: string[]) => {
    try {
      // Enhanced validation
      if (!dbPath || typeof dbPath !== "string") {
        return { success: false, error: "Invalid database path" };
      }
      if (!periodId || typeof periodId !== "string") {
        return { success: false, error: "Invalid period ID" };
      }
      if (!Array.isArray(employeeIds) || employeeIds.length === 0) {
        return { success: false, error: "No employees selected" };
      }

      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };

      const db = entry.db;
      if (!db)
        return { success: false, error: "Database connection is invalid" };

      // Add connection health check
      try {
        await db.select().from(schema.payslips).limit(1).execute();
      } catch (connError) {
        console.error("[IPC] Database connection test failed:", connError);
        return { success: false, error: "Database connection is unhealthy" };
      }

      // Add timeout to prevent hanging transactions
      const transactionPromise = withDatabaseTransaction(
        db,
        async (tx: any) => {
          const now = Math.floor(Date.now() / 1000);
          const payslipTable = schema.payslips;

          // Validate transaction context
          if (!tx) {
            throw new Error("Transaction context is invalid");
          }

          console.log(
            `[IPC] Processing finalisation for ${employeeIds.length} employees`,
          );

          // Check if foreign keys are enabled and temporarily disable them
          try {
            await tx.run(sql`PRAGMA foreign_keys = OFF`);
            console.log("[IPC] Temporarily disabled foreign key constraints");
          } catch (pragmaError) {
            console.warn("[IPC] Could not disable foreign keys:", pragmaError);
          }

          // Get actual payslip IDs for the employees in this period
          const existingPayslips = await tx
            .select({
              id: payslipTable.id,
              employee_id: payslipTable.employee_id,
            })
            .from(payslipTable)
            .where(inArray(payslipTable.employee_id, employeeIds))
            .where(eq(payslipTable.period_id, periodId))
            .all();

          const payslipIds = existingPayslips.map((p: any) => p.id);
          console.log(
            `[IPC] Found ${existingPayslips.length} existing payslips for finalisation`,
          );

          // Check for any existing line items that might cause issues
          if (payslipIds.length > 0) {
            try {
              const lineItemCount = await tx
                .select({ count: sql`COUNT(*)` })
                .from(schema.payslipLineItems)
                .where(inArray(schema.payslipLineItems.payslip_id, payslipIds))
                .get();

              console.log(
                `[IPC] Found ${lineItemCount?.count || 0} basic pay line items`,
              );

              // Also check additions and deductions
              const additionsCount = await tx
                .select({ count: sql`COUNT(*)` })
                .from(schema.payslipAdditionsLineItems)
                .where(
                  inArray(
                    schema.payslipAdditionsLineItems.payslip_id,
                    payslipIds,
                  ),
                )
                .get();

              const deductionsCount = await tx
                .select({ count: sql`COUNT(*)` })
                .from(schema.payslipDeductionsLineItems)
                .where(
                  inArray(
                    schema.payslipDeductionsLineItems.payslip_id,
                    payslipIds,
                  ),
                )
                .get();

              console.log(
                `[IPC] Found ${additionsCount?.count || 0} additions and ${deductionsCount?.count || 0} deductions`,
              );
            } catch (countError) {
              console.warn("[IPC] Could not count line items:", countError);
            }
          }

          // Simple, direct processing - no batching
          for (const empId of employeeIds) {
            if (!empId || typeof empId !== "string") {
              throw new Error(`Invalid employee ID: ${empId}`);
            }

            const existing = await tx
              .select()
              .from(payslipTable)
              .where(eq(payslipTable.employee_id, empId))
              .where(eq(payslipTable.period_id, periodId))
              .get();

            if (!existing) {
              const id = crypto.randomUUID();
              await tx
                .insert(payslipTable)
                .values({
                  id,
                  employee_id: empId,
                  period_id: periodId,
                  created_at: now,
                  updated_at: now,
                })
                .run();
            }
          }
          // Close payslips by employee
          await tx
            .update(payslipTable)
            .set({
              status: "closed",
              finalised_at: now.toString(),
              updated_at: now,
            })
            .where(inArray(payslipTable.employee_id, employeeIds))
            .where(eq(payslipTable.period_id, periodId))
            .run();

          // Open next pay period
          const pp = require("../../drizzle/schema/employer/payPeriod");
          const current = await tx
            .select()
            .from(pp.payPeriods)
            .where(eq(pp.payPeriods.id, periodId))
            .get();
          if (current) {
            const { schedule_id, period_number, tax_year } = current;
            // Determine next period by ordering schedule periods
            const allPeriods = await tx
              .select()
              .from(pp.payPeriods)
              .where(eq(pp.payPeriods.schedule_id, schedule_id))
              .where(eq(pp.payPeriods.tax_year, tax_year))
              .orderBy(pp.payPeriods.period_number)
              .all();
            const idx = allPeriods.findIndex((p: any) => p.id === periodId);
            const next =
              idx >= 0 && idx < allPeriods.length - 1
                ? allPeriods[idx + 1]
                : undefined;
            if (next) {
              // Set current period inactive
              await tx
                .update(pp.payPeriods)
                .set({ active: 0, updated_at: now })
                .where(eq(pp.payPeriods.id, periodId))
                .run();
              // Set next period active
              await tx
                .update(pp.payPeriods)
                .set({ active: 1, updated_at: now })
                .where(eq(pp.payPeriods.id, next.id))
                .run();
              // Ensure payslips exist for next period
              for (const empId of employeeIds) {
                const existsNext = await tx
                  .select()
                  .from(payslipTable)
                  .where(eq(payslipTable.employee_id, empId))
                  .where(eq(payslipTable.period_id, next.id))
                  .get();
                if (!existsNext) {
                  const newId = crypto.randomUUID();
                  await tx
                    .insert(payslipTable)
                    .values({
                      id: newId,
                      employee_id: empId,
                      period_id: next.id,
                      created_at: now,
                      updated_at: now,
                    })
                    .run();
                }
              }
              // Set payslips for next period to status 'open' for relevant employees
              await tx
                .update(payslipTable)
                .set({ status: "open", updated_at: now })
                .where(inArray(payslipTable.employee_id, employeeIds))
                .where(eq(payslipTable.period_id, next.id))
                .run();
            }
          }

          // Re-enable foreign key constraints
          try {
            await tx.run(sql`PRAGMA foreign_keys = ON`);
            console.log("[IPC] Re-enabled foreign key constraints");
          } catch (pragmaError) {
            console.warn(
              "[IPC] Could not re-enable foreign keys:",
              pragmaError,
            );
          }

          // Verify database integrity after the operation
          try {
            await tx.run(sql`PRAGMA integrity_check`);
            console.log("[IPC] Database integrity check passed");
          } catch (integrityError) {
            console.error(
              "[IPC] Database integrity check failed:",
              integrityError,
            );
            throw new Error(
              "Database integrity compromised during finalisation",
            );
          }
        },
        {
          timeoutMs: 30000, // 30 second timeout for complex operation
          operationName: "finalisePayslips",
        },
      );

      // Add timeout wrapper
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Transaction timeout")), 15000);
      });

      await Promise.race([transactionPromise, timeoutPromise]);
      return { success: true };
    } catch (err: any) {
      console.error("[IPC] Error in finalisePayslips:", err);
      return {
        success: false,
        error: err.message || "Unknown error occurred during finalisation",
      };
    }
  },
);

// Get payslip statuses for all employees in a period
ipcMain.handle(
  "employerDb:getPayslipStatusesForPeriod",
  async (_event, dbPath: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const payslips = await db
        .select({
          employee_id: schema.payslips.employee_id,
          status: schema.payslips.status,
        })
        .from(schema.payslips)
        .where(eq(schema.payslips.period_id, periodId))
        .all();
      return { success: true, payslips };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Reopen selected payslips
ipcMain.handle(
  "employerDb:reopenPayslips",
  async (_event, dbPath: string, periodId: string, employeeIds: string[]) => {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;

    // Validate inputs
    if (!periodId || !Array.isArray(employeeIds) || employeeIds.length === 0) {
      return {
        success: false,
        error: "Invalid parameters: periodId and employeeIds are required",
      };
    }

    try {
      const result = await db.transaction(async (tx: any) => {
        const now = Math.floor(Date.now() / 1000);
        const payslipTable = schema.payslips;

        // Ensure payslips exist for each employee in this period
        for (const empId of employeeIds) {
          if (!empId || typeof empId !== "string") {
            throw new Error(`Invalid employee ID: ${empId}`);
          }

          const existing = await tx
            .select()
            .from(payslipTable)
            .where(eq(payslipTable.employee_id, empId))
            .where(eq(payslipTable.period_id, periodId))
            .get();
          if (!existing) {
            const id = crypto.randomUUID();
            await tx
              .insert(payslipTable)
              .values({
                id,
                employee_id: empId,
                period_id: periodId,
                status: "open",
                created_at: now,
                updated_at: now,
              })
              .run();
          }
        }

        // Reopen payslips by setting status to 'open' and clearing finalised_at
        const updateResult = await tx
          .update(payslipTable)
          .set({ status: "open", finalised_at: null, updated_at: now })
          .where(inArray(payslipTable.employee_id, employeeIds))
          .where(eq(payslipTable.period_id, periodId))
          .run();

        return { success: true, updatedCount: updateResult.changes || 0 };
      });

      return result;
    } catch (err: any) {
      console.error("[IPC] Error reopening payslips:", err);
      return { success: false, error: err.message || "Unknown error occurred" };
    }
  },
);
