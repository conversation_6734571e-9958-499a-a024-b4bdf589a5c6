"use client";
console.log("[LAYOUT] layout.tsx loaded");
import { Lato, Open_Sans } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/providers/theme-provider";
import { TaxYearProvider } from "@/providers/tax-year-provider";
import { EmployerDBProvider } from "@/providers/employer-db-provider";
import { MainNavbar } from "@/components/layouts/main-navbar";
import { ActionToolbar } from "@/components/layouts/action-toolbar";
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
  MutationCache,
} from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { ErrorBoundary } from "@/components/error-boundary";

// Load Lato font (primary)
const lato = Lato({
  subsets: ["latin"],
  weight: ["300", "400", "700", "900"],
  variable: "--font-lato",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Create the client only once per app instance with optimized configuration
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Data remains fresh for different periods based on volatility
            staleTime: 5 * 60 * 1000, // 5 minutes default
            // Keep unused data cached for 30 minutes
            gcTime: 30 * 60 * 1000,
            // Retry 3 times with exponential backoff
            retry: (failureCount, error) => {
              // Don't retry if cancelled or circuit breaker is open
              if (
                error.message === "Request cancelled" ||
                error.message.includes("Circuit breaker is OPEN")
              ) {
                return false;
              }
              return failureCount < 3;
            },
            retryDelay: (attemptIndex) =>
              Math.min(1000 * 2 ** attemptIndex, 30000),
            // Don't refetch on window focus for desktop app
            refetchOnWindowFocus: false,
            // Refetch on reconnect
            refetchOnReconnect: true,
          },
          mutations: {
            // Retry mutations once
            retry: 1,
            retryDelay: 1000,
          },
        },
        queryCache: new QueryCache({
          onError: (error, query) => {
            // Log errors but don't display them if data exists
            console.error(`[QueryCache] Query error: ${error.message}`, {
              queryKey: query.queryKey,
              error,
            });
          },
        }),
        mutationCache: new MutationCache({
          onError: (error, variables, context, mutation) => {
            console.error(`[MutationCache] Mutation error: ${error.message}`, {
              mutationKey: mutation.options.mutationKey,
              error,
            });
          },
        }),
      }),
  );

  // Global error handler for unhandled promise rejections and errors
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error("[Global] Unhandled promise rejection:", event.reason);
      // Prevent the default behavior (which would crash the app)
      event.preventDefault();
    };

    const handleError = (event: ErrorEvent) => {
      console.error("[Global] Unhandled error:", event.error);
      // Prevent the default behavior
      event.preventDefault();
    };

    // Add global error handlers
    window.addEventListener("unhandledrejection", handleUnhandledRejection);
    window.addEventListener("error", handleError);

    return () => {
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection,
      );
      window.removeEventListener("error", handleError);
    };
  }, []);

  return (
    <html lang="en" suppressHydrationWarning className={`${lato.variable}`}>
      <QueryClientProvider client={queryClient}>
        <TaxYearProvider>
          <EmployerDBProvider>
            <ThemeProvider defaultTheme="light">
              <body className="bg-background flex h-screen min-h-0 flex-col overflow-hidden px-2 font-sans subpixel-antialiased">
                <ErrorBoundary
                  onError={(error, errorInfo) => {
                    console.error(
                      "[Layout] App-level error caught:",
                      error,
                      errorInfo,
                    );
                  }}
                >
                  <MainNavbar />
                  <ActionToolbar />
                  <main className="flex min-h-0 flex-1 flex-col">
                    {children}
                  </main>
                </ErrorBoundary>
              </body>
            </ThemeProvider>
          </EmployerDBProvider>
        </TaxYearProvider>
      </QueryClientProvider>
    </html>
  );
}
