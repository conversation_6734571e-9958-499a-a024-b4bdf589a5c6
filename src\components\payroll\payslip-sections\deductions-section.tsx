"use client";

import React, { useEffect, useState, useRef } from "react";
import { Trash2 } from "lucide-react";
import {
  ReadOnlyAwareButton,
  ReadOnlyAwarePayslipNumberInput,
  ReadOnlyAwareRepeatingControl,
  ReadOnlyAwareZeroizeControl,
  ReadOnlyAwareSectionHeader,
} from "@/components/payroll/ui/readonly-aware-components";
import {
  usePayslip,
  useCreatePayslipMutation,
} from "@/hooks/tanstack-query/usePayslip";
import {
  usePayslipDeductionsItemTypes,
  useUpsertPayslipDeductionsLineItemMutation,
  useDeletePayslipDeductionsLineItemMutation,
  useClearAllPayslipDeductionsLineItemsMutation,
  useDeleteAllPayslipDeductionsLineItemsMutation,
} from "@/hooks/tanstack-query/usePayslipDeductions";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { useQueryClient } from "@tanstack/react-query";
import { useMemoryTracking } from "@/utils/memory-monitor";

interface DeductionItem {
  id: string;
  type: string;
  name: string;
  amount: number;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface DeductionsSectionData {
  items: DeductionItem[];
}

interface PayslipDeductionsSectionProps {
  employeeId: string;
  periodId: string;
}

const PayslipDeductionsSection: React.FC<PayslipDeductionsSectionProps> = ({
  employeeId,
  periodId,
}) => {
  // Memory tracking for crash prevention
  useMemoryTracking("PayslipDeductionsSection");

  const initRef = useRef(false);
  const itemsRef = useRef<DeductionItem[]>([]);
  const mountedRef = useRef(true);
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const { data, isLoading } = usePayslip(employeeId, periodId);
  const { data: itemTypes } = usePayslipDeductionsItemTypes();
  const upsert = useUpsertPayslipDeductionsLineItemMutation(
    employeeId,
    periodId,
  );
  const del = useDeletePayslipDeductionsLineItemMutation(employeeId, periodId);
  const clearAll = useClearAllPayslipDeductionsLineItemsMutation(
    employeeId,
    periodId,
  );
  const deleteAll = useDeleteAllPayslipDeductionsLineItemsMutation(
    employeeId,
    periodId,
  );
  const createPayslip = useCreatePayslipMutation(employeeId, periodId);
  const queryClient = useQueryClient();

  // Cancel any pending mutations when switching employees/periods
  useEffect(() => {
    return () => {
      // Cancel pending mutations to prevent race conditions
      upsert.reset();
      del.reset();
      clearAll.reset();
      deleteAll.reset();
      createPayslip.reset();
    };
  }, [employeeId, periodId]);

  const [deductionsData, setDeductionsData] = useState<DeductionsSectionData>({
    items: [],
  });

  const payId = (data as any)?.payslip?.id;
  const items = deductionsData.items || [];

  // Keep ref in sync with state
  useEffect(() => {
    itemsRef.current = items;
  }, [items]);

  // Reset when switching employees/periods
  useEffect(() => {
    mountedRef.current = true;
    setDeductionsData({ items: [] });
    initRef.current = false;

    // Cleanup function to prevent memory leaks
    return () => {
      // Clear any pending operations
      initRef.current = false;
      mountedRef.current = false;
    };
  }, [employeeId, periodId]);

  // Load data from database whenever data or itemTypes change
  useEffect(() => {
    if (data && itemTypes) {
      const deductions = (data as any).deductions || [];

      // Always load data from database, regardless of initRef state
      const items = deductions
        .map((item: any) => {
          const typeMeta = itemTypes.find((t) => t.id === item.item_type_id);
          if (!typeMeta) {
            return null;
          }
          const mappedItem = {
            id: item.id, // Use the real database ID
            type: typeMeta.code,
            name: typeMeta.display_label,
            amount: item.amount,
            isRepeating: item.is_repeating,
            zeroizeNext: item.zeroise_next,
          } as DeductionItem;
          return mappedItem;
        })
        .filter(Boolean) as DeductionItem[];

      // Force update the state even if items array looks the same
      setDeductionsData({ items: [...items] });
      initRef.current = true; // Mark as initialized after loading data
    } else if (data && itemTypes && (data as any).deductions?.length === 0) {
      // Explicitly handle empty deductions case
      setDeductionsData({ items: [] });
      initRef.current = true;
    }
  }, [data, itemTypes, employeeId, periodId]); // Added employeeId and periodId to dependencies

  // Auto-save when items change (with debouncing)
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear any existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
      saveTimeoutRef.current = null;
    }

    if (initRef.current && items.length > 0 && mountedRef.current) {
      saveTimeoutRef.current = setTimeout(() => {
        // Check if component is still mounted before saving
        if (initRef.current && mountedRef.current) {
          saveChanges();
        }
        saveTimeoutRef.current = null;
      }, 300);
    }

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }
    };
  }, [items]);

  // Handle input changes for a specific item
  const handleItemChange = (
    index: number,
    field: string,
    value: string | boolean | number,
  ) => {
    const newItems = [...items];
    const item = { ...newItems[index] };

    // Type checking and conversion
    if (typeof value === "string" && field === "amount") {
      (item as any)[field] = parseFloat(value) || 0;
    } else {
      (item as any)[field] = value;
    }

    // If isRepeating is set to false, also set zeroizeNext to false
    if (field === "isRepeating" && value === false) {
      item.zeroizeNext = false;
    }

    newItems[index] = item;
    setDeductionsData({ items: newItems });

    // Save immediately when user changes values
    setTimeout(() => saveChanges(), 100);
  };

  // Database operations
  const saveChanges = () => {
    const currentItems = itemsRef.current;
    if (!payId) {
      // Only create a payslip once: only invoke when idle
      if (createPayslip.status === "idle") {
        createPayslip.mutate(undefined, {
          onSuccess: (slip) => upsertAll(slip.id, currentItems),
        });
      }
    } else {
      upsertAll(payId, currentItems);
    }
  };

  const upsertAll = (
    payslipId: string,
    itemsToSave: DeductionItem[] = itemsRef.current,
  ) => {
    itemsToSave.forEach((item) => {
      const itemType = itemTypes?.find((t) => t.code === item.type);
      if (!itemType) return;

      const payload: any = {
        payslip_id: payslipId,
        item_type_id: itemType.id,
        amount: item.amount,
        is_repeating: item.isRepeating,
        zeroise_next: item.zeroizeNext,
      };

      // Only include ID if it's a real database ID (not a temporary one)
      // Real database IDs are UUIDs, temporary ones start with "temp-"
      const isRealDbId = item.id && !item.id.startsWith("temp-");
      if (isRealDbId) {
        payload.id = item.id;
      }

      const elementTempId = item.id;
      upsert.mutate(payload, {
        onSuccess: (lineItem) => {
          // On create, remap the UI element id to the real DB id
          if (!isRealDbId) {
            setDeductionsData((current) => ({
              items: current.items.map((e) =>
                e.id === elementTempId ? { ...e, id: lineItem.id } : e,
              ),
            }));
          }
          if (dbPath)
            queryClient.invalidateQueries({
              queryKey: ["payslip", dbPath, employeeId, periodId],
            });
        },
      });
    });
  };

  // Add a new deduction item
  const addDeductionItem = (type: string, name: string) => {
    const newItem: DeductionItem = {
      id: `temp-${type}-${Date.now()}-${Math.random()}`,
      type,
      name,
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    setDeductionsData({ items: [...items, newItem] });
    // Save the new item immediately
    setTimeout(() => saveChanges(), 100);
  };

  const addSalarySacrificeItem = () =>
    addDeductionItem("salary-sacrifice", "Salary Sacrifice");
  const addUnpaidLeaveItem = () =>
    addDeductionItem("unpaid-leave", "Unpaid Leave");
  const addAdvancesItem = () => addDeductionItem("advance", "Advances");
  const addPayrollGivingItem = () =>
    addDeductionItem("payroll-giving", "Payroll Giving");

  // Remove an item
  const removeItem = (id: string) => {
    // detect and delete removed items
    const prevIds = items.map((e) => e.id);
    const newItems = items.filter((item) => item.id !== id);
    setDeductionsData({ items: newItems });
    const newIds = newItems.map((e) => e.id);
    const removedIds = prevIds.filter((id) => !newIds.includes(id));
    removedIds.forEach((id) => {
      // Only delete if it's a real DB id (not temporary)
      const isRealDbId = !id.startsWith("temp-");
      if (isRealDbId) {
        del.mutate(id, {
          onSuccess: () => {
            if (dbPath)
              queryClient.invalidateQueries({
                queryKey: ["payslip", dbPath, employeeId, periodId],
              });
          },
        });
      }
    });
  };

  // Clear all values (set to zero but keep items)
  const clearAllValues = () => {
    if (payId) {
      clearAll.mutate(payId, {
        onSuccess: () => {
          const clearedItems = items.map((item) => {
            return { ...item, amount: 0 };
          });
          setDeductionsData({ items: clearedItems });
        },
      });
    } else {
      const clearedItems = items.map((item) => {
        return { ...item, amount: 0 };
      });
      setDeductionsData({ items: clearedItems });
    }
  };

  // Remove all items
  const removeAllItems = () => {
    if (payId) {
      deleteAll.mutate(payId, {
        onSuccess: () => {
          setDeductionsData({ items: [] });
        },
      });
    } else {
      setDeductionsData({ items: [] });
    }
  };

  return (
    <div>
      <ReadOnlyAwareSectionHeader
        title="Deductions"
        sectionType="deductions"
        addButtons={[
          { label: "Salary Sacrifice", onClick: addSalarySacrificeItem },
          { label: "Unpaid Leave", onClick: addUnpaidLeaveItem },
          { label: "Advances", onClick: addAdvancesItem },
          { label: "Payroll Giving", onClick: addPayrollGivingItem },
        ]}
        actionButtons={[
          {
            label: "Clear Values",
            onClick: clearAllValues,
            variant: "outline",
          },
          {
            label: "Delete All",
            onClick: removeAllItems,
            variant: "destructive",
          },
        ]}
      />

      {/* Deductions container */}
      <div>
        <div className="mb-3 min-h-[28px] space-y-0">
          {items.map((item) => {
            const itemIndex = items.findIndex((i) => i.id === item.id);
            return (
              <div key={item.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <ReadOnlyAwareButton
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeItem(item.id)}
                  >
                    <Trash2 className="size-4" />
                  </ReadOnlyAwareButton>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[120px_90px_auto] items-center gap-1">
                        <span className="text-foreground pr-2 text-sm whitespace-nowrap">
                          {item.name}
                        </span>
                        <ReadOnlyAwarePayslipNumberInput
                          id={`amount-${item.id}`}
                          className="h-7 w-full text-sm"
                          value={item.amount}
                          onChange={(value) =>
                            handleItemChange(itemIndex, "amount", value ?? 0)
                          }
                          placeholder=""
                          decimalPlaces={2}
                          allowCalculations={true}
                        />
                        <div className="ml-4 flex items-center">
                          <ReadOnlyAwareRepeatingControl
                            id={item.id}
                            isChecked={item.isRepeating}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ReadOnlyAwareZeroizeControl
                            id={item.id}
                            isChecked={item.zeroizeNext}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!item.isRepeating}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PayslipDeductionsSection;
