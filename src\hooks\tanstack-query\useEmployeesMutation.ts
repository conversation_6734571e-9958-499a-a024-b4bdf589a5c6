import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { addEmployee, updateEmployee } from "@/services/employerDbService";
import type { Employee } from "@/lib/schemas/employee";

/**
 * TanStack Query mutation hook for adding or updating an employee in the employer DB.
 * Decides between add/update by presence of employee.id (new = no id).
 * Invalidates the employees query on success.
 */
export function useEmployeesMutation() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation<Employee, Error, Employee>({
    mutationFn: async (employee) => {
      if (!dbPath) throw new Error("No active employer DB");

      // Enhanced validation and logging
      console.log("[EmployeeMutation] Processing employee:", {
        hasId: !!employee.id,
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
      });

      // Validate employee data
      if (!employee.firstName || !employee.lastName) {
        throw new Error("Employee first name and last name are required");
      }

      if (!employee.id || employee.id === "") {
        console.log("[EmployeeMutation] Adding new employee");
        return await addEmployee(dbPath, employee);
      } else {
        console.log("[EmployeeMutation] Updating existing employee");
        return await updateEmployee(dbPath, employee);
      }
    },
    onSuccess: (result) => {
      console.log("[EmployeeMutation] Success:", result);
      queryClient.invalidateQueries({ queryKey: ["employees", dbPath] });
    },
    onError: (error) => {
      console.error("[EmployeeMutation] Error:", error);
    },
  });
}
