import {
  sqliteTable,
  text,
  integer,
  real,
  index,
  uniqueIndex,
} from "drizzle-orm/sqlite-core";
import { sql } from "drizzle-orm";
import { z } from "zod";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

// Allowed payslip statuses
const PAYSALIP_STATUSES = ["open", "closed", "future", "scheduled"] as const;

// Payslips table
export const payslips = sqliteTable(
  "payslips",
  {
    id: text("id").primaryKey().notNull(),
    employee_id: text("employee_id")
      .notNull()
      .references(() => require("./employee.schema").employee.id),
    period_id: text("period_id")
      .notNull()
      .references(() => require("./payPeriod").payPeriods.id),
    status: text("status", { enum: PAYSALIP_STATUSES })
      .notNull()
      .default("open"),
    finalised_at: text("finalised_at"),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [
    index("idx_payslips_employee").on(table.employee_id),
    index("idx_payslips_period").on(table.period_id),
    // Composite index for the most common query pattern
    index("idx_payslips_employee_period").on(
      table.employee_id,
      table.period_id,
    ),
    // Index for status queries (used in finalise/reopen operations)
    index("idx_payslips_status").on(table.status),
    // Composite index for period + status queries
    index("idx_payslips_period_status").on(table.period_id, table.status),
  ],
);

// Zod schemas for Payslip entities
export const payslipInsertSchema = createInsertSchema(payslips);
export type PayslipInsert = z.infer<typeof payslipInsertSchema>;
export const payslipSelectSchema = createSelectSchema(payslips);
export type Payslip = z.infer<typeof payslipSelectSchema>;

// Payslip item types table
export const payslipItemTypes = sqliteTable(
  "payslip_item_types",
  {
    id: text("id").primaryKey().notNull(),
    code: text("code").notNull(),
    section: text("section").notNull(),
    display_label: text("display_label").notNull(),
    default_units: text("default_units"),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [uniqueIndex("uk_payslip_item_types_code").on(table.code)],
);

export const payslipItemTypeInsertSchema = createInsertSchema(payslipItemTypes);
export type PayslipItemTypeInsert = z.infer<typeof payslipItemTypeInsertSchema>;
export const payslipItemTypeSelectSchema = createSelectSchema(payslipItemTypes);
export type PayslipItemType = z.infer<typeof payslipItemTypeSelectSchema>;

// Payslip line items table
export const payslipLineItems = sqliteTable(
  "payslip_line_items",
  {
    id: text("id").primaryKey().notNull(),
    payslip_id: text("payslip_id")
      .notNull()
      .references(() => payslips.id),
    item_type_id: text("item_type_id")
      .notNull()
      .references(() => payslipItemTypes.id),
    units: real("units"),
    rate_or_amount: real("rate_or_amount").notNull(),
    is_custom: integer("is_custom", { mode: "boolean" })
      .notNull()
      .default(false),
    is_repeating: integer("is_repeating", { mode: "boolean" })
      .notNull()
      .default(false),
    zeroise_next: integer("zeroise_next", { mode: "boolean" })
      .notNull()
      .default(false),
    notes: text("notes"),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [index("idx_line_items_payslip").on(table.payslip_id)],
);

export const payslipLineItemInsertSchema = createInsertSchema(payslipLineItems);
export type PayslipLineItemInsert = z.infer<typeof payslipLineItemInsertSchema>;
export const payslipLineItemSelectSchema = createSelectSchema(payslipLineItems);
export type PayslipLineItem = z.infer<typeof payslipLineItemSelectSchema>;

// Payslip notes table
export const payslipNotes = sqliteTable(
  "payslip_notes",
  {
    id: text("id").primaryKey().notNull(),
    payslip_id: text("payslip_id")
      .notNull()
      .references(() => payslips.id),
    content: text("content").notNull(),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [index("idx_payslip_notes_payslip").on(table.payslip_id)],
);

export const payslipNoteInsertSchema = createInsertSchema(payslipNotes);
export type PayslipNoteInsert = z.infer<typeof payslipNoteInsertSchema>;
export const payslipNoteSelectSchema = createSelectSchema(payslipNotes);
export type PayslipNote = z.infer<typeof payslipNoteSelectSchema>;

// Payslip additions item types table
export const payslipAdditionsItemTypes = sqliteTable(
  "payslip_additions_item_types",
  {
    id: text("id").primaryKey().notNull(),
    code: text("code").notNull(),
    display_label: text("display_label").notNull(),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [
    uniqueIndex("uk_payslip_additions_item_types_code").on(table.code),
  ],
);

export const payslipAdditionsItemTypeInsertSchema = createInsertSchema(
  payslipAdditionsItemTypes,
);
export type PayslipAdditionsItemTypeInsert = z.infer<
  typeof payslipAdditionsItemTypeInsertSchema
>;
export const payslipAdditionsItemTypeSelectSchema = createSelectSchema(
  payslipAdditionsItemTypes,
);
export type PayslipAdditionsItemType = z.infer<
  typeof payslipAdditionsItemTypeSelectSchema
>;

// Payslip additions line items table
export const payslipAdditionsLineItems = sqliteTable(
  "payslip_additions_line_items",
  {
    id: text("id").primaryKey().notNull(),
    payslip_id: text("payslip_id")
      .notNull()
      .references(() => payslips.id),
    item_type_id: text("item_type_id")
      .notNull()
      .references(() => payslipAdditionsItemTypes.id),
    amount: real("amount").notNull(),
    is_repeating: integer("is_repeating", { mode: "boolean" })
      .notNull()
      .default(false),
    zeroise_next: integer("zeroise_next", { mode: "boolean" })
      .notNull()
      .default(false),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [index("idx_additions_line_items_payslip").on(table.payslip_id)],
);

export const payslipAdditionsLineItemInsertSchema = createInsertSchema(
  payslipAdditionsLineItems,
);
export type PayslipAdditionsLineItemInsert = z.infer<
  typeof payslipAdditionsLineItemInsertSchema
>;
export const payslipAdditionsLineItemSelectSchema = createSelectSchema(
  payslipAdditionsLineItems,
);
export type PayslipAdditionsLineItem = z.infer<
  typeof payslipAdditionsLineItemSelectSchema
>;

// Payslip deductions item types table
export const payslipDeductionsItemTypes = sqliteTable(
  "payslip_deductions_item_types",
  {
    id: text("id").primaryKey().notNull(),
    code: text("code").notNull(),
    display_label: text("display_label").notNull(),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [
    uniqueIndex("uk_payslip_deductions_item_types_code").on(table.code),
  ],
);

export const payslipDeductionsItemTypeInsertSchema = createInsertSchema(
  payslipDeductionsItemTypes,
);
export type PayslipDeductionsItemTypeInsert = z.infer<
  typeof payslipDeductionsItemTypeInsertSchema
>;
export const payslipDeductionsItemTypeSelectSchema = createSelectSchema(
  payslipDeductionsItemTypes,
);
export type PayslipDeductionsItemType = z.infer<
  typeof payslipDeductionsItemTypeSelectSchema
>;

// Payslip deductions line items table
export const payslipDeductionsLineItems = sqliteTable(
  "payslip_deductions_line_items",
  {
    id: text("id").primaryKey().notNull(),
    payslip_id: text("payslip_id")
      .notNull()
      .references(() => payslips.id),
    item_type_id: text("item_type_id")
      .notNull()
      .references(() => payslipDeductionsItemTypes.id),
    amount: real("amount").notNull(),
    is_repeating: integer("is_repeating", { mode: "boolean" })
      .notNull()
      .default(false),
    zeroise_next: integer("zeroise_next", { mode: "boolean" })
      .notNull()
      .default(false),
    created_at: integer("created_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
    updated_at: integer("updated_at")
      .notNull()
      .default(sql`(strftime('%s','now'))`),
  },
  (table) => [index("idx_deductions_line_items_payslip").on(table.payslip_id)],
);

export const payslipDeductionsLineItemInsertSchema = createInsertSchema(
  payslipDeductionsLineItems,
);
export type PayslipDeductionsLineItemInsert = z.infer<
  typeof payslipDeductionsLineItemInsertSchema
>;
export const payslipDeductionsLineItemSelectSchema = createSelectSchema(
  payslipDeductionsLineItems,
);
export type PayslipDeductionsLineItem = z.infer<
  typeof payslipDeductionsLineItemSelectSchema
>;
